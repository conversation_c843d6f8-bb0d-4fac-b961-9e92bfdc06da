"use client"

import Image from "next/image";
import ProfileBio, { ProfileBioProps } from "./ProfileBio";
import Link from "next/link";

export interface ArticleCardProps {
    id: number;
    imageUrl: string;
    imageAlt: string;
    date: string;
    title: string;
    description: string;
    category: string;
    author: ProfileBioProps;
}

const ArticleCard = ({ imageUrl, imageAlt, date, title, description, 
                        category, author}: ArticleCardProps) => {
    const formattedDate = new Date(date + "T12:00:00").toLocaleDateString("en-US", {
        month: "long",
        day: "numeric",
        year: "numeric",
    });

    return (
        <div className="flex flex-col justify-start items-start gap-5 bg-white rounded-xl overflow-hidden">
            {/* Image Post */}
            <div className="relative w-full h-[300px] rounded-xl overflow-hidden">
                <Image
                    src={imageUrl}
                    alt={imageAlt}
                    fill
                    className="object-cover"
                />
            </div>
            {/* Text */}
            <div className="flex flex-col gap-2">
                <p className="text-sm text-gray-500">
                    {formattedDate}
                </p>
                <Link href="/" className="font-bold text-3xl text-default-900 hover:underline">
                    {title}
                </Link>
                <p className="text-base text-default-600">
                    {description}
                </p>
            </div>
            {/* Author */}
            <div>
                <ProfileBio {...author} />
            </div>
            <div className="w-[107px] h-[32px] bg-default-100 hover:bg-default-200 rounded-3xl flex justify-center items-center">
                <button className="text-base text-default-600">
                    {category}
                </button>
            </div>
        </div>
    );

}

export default ArticleCard;
//end